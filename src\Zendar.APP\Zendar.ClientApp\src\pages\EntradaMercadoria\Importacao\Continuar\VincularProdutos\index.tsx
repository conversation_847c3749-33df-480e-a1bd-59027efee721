import { But<PERSON>, Stack } from '@chakra-ui/react';
import { toast } from 'react-toastify';

import { useEntradaMercadoriaDadosCadastroContext } from 'store/EntradaMercadoria/EntradaMercadoriaDadosCadastro';
import { useEntradaMercadoriaEtapasContext } from 'store/EntradaMercadoria/EntradaMercadoriaEtapas';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import {
  Container,
  Body,
  Footer,
  StepDescriptionAccordion,
} from 'components/update/Steps/StepContent';

import { ListagemProdutos } from './components/ListagemProdutos';
import TotalizadoresFixos from './components/TotalizadoresFixos';
import { useProdutosVinculacao } from './hooks/useProdutosVinculacao';

export function VincularProdutos() {
  const { nextStep, previousStep } = useEntradaMercadoriaEtapasContext();
  const {
    entradaMercadoriaId,
    descartarEntradaMercadoria,
    voltarParaListagem,
    temPermissaoExcluir,
    isReadOnly,
    menuIsOpen,
  } = useEntradaMercadoriaDadosCadastroContext();

  const { todosProdutosVinculados, isLoading, informacoesRodape } =
    useProdutosVinculacao(entradaMercadoriaId ?? null);

  // Debug: Log para verificar os valores de informacoesRodape
  console.log('Debug - informacoesRodape:', informacoesRodape);
  console.log('Debug - totalProdutos:', informacoesRodape.totalProdutos);
  console.log('Debug - quantidadeItens:', informacoesRodape.quantidadeItens);
  console.log(
    'Debug - valorTotalProdutos:',
    informacoesRodape.valorTotalProdutos
  );

  function handleDescartarEntradaMercadoria() {
    descartarEntradaMercadoria();
  }

  function handleVoltar() {
    previousStep();
  }

  function handleSalvarRascunho() {
    voltarParaListagem();
  }

  function handleAvancar() {
    if (todosProdutosVinculados) {
      nextStep();
    } else {
      toast.warning('É necessário vincular todos os produtos para continuar.');
    }
  }

  return (
    <>
      {isLoading && <LoadingPadrao />}
      <Container mt="6px">
        <StepDescriptionAccordion
          stepNumber={2}
          title="Lista de produtos"
          description='Todos os produtos contidos na nota fiscal precisam ser vinculados a um produto existente no sistema. Clique em "vincular ao sistema" em cada um dos itens listados abaixo para realizar esta ação. Caso exista um novo produto você poderá cadastrá-lo na própria tela de vinculação.'
        />
        <Body>
          <ListagemProdutos entradaMercadoriaId={entradaMercadoriaId ?? null} />
        </Body>
      </Container>
      {(() => {
        const shouldShowTotalizadores = informacoesRodape.totalProdutos > 0;
        console.log(
          'Debug - shouldShowTotalizadores:',
          shouldShowTotalizadores
        );
        console.log(
          'Debug - informacoesRodape.totalProdutos > 0:',
          informacoesRodape.totalProdutos > 0
        );

        // Forçar exibição para teste
        return (
          <TotalizadoresFixos
            quantidadeItens={informacoesRodape.quantidadeItens || 0}
            totalProdutos={informacoesRodape.totalProdutos || 0}
            valorTotalProdutos={informacoesRodape.valorTotalProdutos || 0}
          />
        );
      })()}
      <Footer
        justifyContent="space-between"
        position="fixed"
        bottom="0px"
        left={menuIsOpen ? '180px' : '77px'}
        right="0"
        bg="gray.50"
        py="16px"
        px="45px"
        pl="52px"
        w={`calc(100% - ${menuIsOpen ? '210px' : '108px'})`}
      >
        <Button
          variant="outlineDefault"
          borderRadius="full"
          w="full"
          maxW={{ base: 'full', md: '160px' }}
          onClick={handleVoltar}
        >
          Voltar
        </Button>
        <Stack
          w="full"
          justifyContent="flex-end"
          direction={{ base: 'column', md: 'row' }}
          spacing={{ base: 2, sm: 4, md: 6 }}
        >
          {isReadOnly ? (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '196px' }}
              onClick={voltarParaListagem}
            >
              Voltar para a listagem
            </Button>
          ) : (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={handleDescartarEntradaMercadoria}
              isDisabled={!temPermissaoExcluir}
            >
              Descartar
            </Button>
          )}
          {!isReadOnly && (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={handleSalvarRascunho}
            >
              Salvar e sair
            </Button>
          )}
          <Button
            colorScheme="purple"
            borderRadius="full"
            w="full"
            maxW={{ base: 'full', md: '160px' }}
            onClick={handleAvancar}
            isDisabled={!todosProdutosVinculados}
          >
            Avançar
          </Button>
        </Stack>
      </Footer>
    </>
  );
}
